const BuildFixAgent = require('../src/build/buildFixAgent');
const fs = require('fs-extra');
const path = require('path');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

describe('BuildFixAgent', () => {
  let agent;
  const testProjectPath = '/tmp/test-project';

  beforeEach(() => {
    agent = new BuildFixAgent(testProjectPath, {
      buildCommand: 'npm run build',
      maxRetries: 2,
      verbose: true
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('初始化', () => {
    it('should initialize with correct options', () => {
      expect(agent.projectPath).toBe(testProjectPath);
      expect(agent.options.buildCommand).toBe('npm run build');
      expect(agent.options.maxRetries).toBe(2);
      expect(agent.agentState).toBeDefined();
      expect(agent.agentTools).toBeDefined();
    });

    it('should initialize agent tools correctly', () => {
      const tools = agent.agentTools;
      
      expect(tools.analyze_project).toBeDefined();
      expect(tools.read_file).toBeDefined();
      expect(tools.write_file).toBeDefined();
      expect(tools.list_files).toBeDefined();
      expect(tools.execute_command).toBeDefined();
      expect(tools.analyze_error).toBeDefined();
      
      // 检查工具结构
      expect(tools.read_file.description).toBeDefined();
      expect(tools.read_file.parameters).toBeDefined();
      expect(tools.read_file.handler).toBeDefined();
    });
  });

  describe('Agent 工具执行', () => {
    it('should execute read_file tool correctly', async () => {
      // Mock fs.pathExists and fs.readFile
      jest.spyOn(fs, 'pathExists').mockResolvedValue(true);
      jest.spyOn(fs, 'readFile').mockResolvedValue('test file content');

      const result = await agent.executeAgentTool('read_file', { 
        file_path: 'test.js' 
      });

      expect(result.success).toBe(true);
      expect(result.content).toBe('test file content');
      expect(fs.pathExists).toHaveBeenCalledWith(path.join(testProjectPath, 'test.js'));
    });

    it('should handle read_file tool errors', async () => {
      jest.spyOn(fs, 'pathExists').mockResolvedValue(false);

      const result = await agent.executeAgentTool('read_file', { 
        file_path: 'nonexistent.js' 
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('文件不存在');
    });

    it('should execute write_file tool correctly', async () => {
      jest.spyOn(fs, 'pathExists').mockResolvedValue(false);
      jest.spyOn(fs, 'ensureDir').mockResolvedValue();
      jest.spyOn(fs, 'writeFile').mockResolvedValue();

      const result = await agent.executeAgentTool('write_file', {
        file_path: 'test.js',
        content: 'console.log("test");',
        backup: false
      });

      expect(result.success).toBe(true);
      expect(result.message).toBe('文件写入成功');
      expect(fs.writeFile).toHaveBeenCalledWith(
        path.join(testProjectPath, 'test.js'),
        'console.log("test");',
        'utf8'
      );
    });

    it('should execute list_files tool correctly', async () => {
      jest.spyOn(fs, 'pathExists').mockResolvedValue(true);
      jest.spyOn(fs, 'readdir').mockResolvedValue(['file1.js', 'file2.vue', 'file3.ts']);

      const result = await agent.executeAgentTool('list_files', {
        directory: 'src',
        pattern: '*.js'
      });

      expect(result.success).toBe(true);
      expect(result.files).toEqual(['file1.js']);
      expect(result.count).toBe(1);
    });

    it('should execute analyze_project tool correctly', async () => {
      jest.spyOn(fs, 'pathExists')
        .mockResolvedValueOnce(true)  // src exists
        .mockResolvedValueOnce(true)  // public exists
        .mockResolvedValueOnce(true); // package.json exists
      
      jest.spyOn(fs, 'readJson').mockResolvedValue({
        dependencies: {
          vue: '^3.0.0'
        },
        devDependencies: {
          '@vue/cli-service': '^4.0.0'
        }
      });

      const result = await agent.executeAgentTool('analyze_project', { 
        focus: 'all' 
      });

      expect(result.success).toBe(true);
      expect(result.result.structure.hasSrcDir).toBe(true);
      expect(result.result.structure.type).toBe('vue_project');
      expect(result.result.dependencies.vue).toBe('^3.0.0');
    });

    it('should handle unknown tool gracefully', async () => {
      const result = await agent.executeAgentTool('unknown_tool', {});

      expect(result.success).toBe(false);
      expect(result.error).toBe('未知工具: unknown_tool');
    });
  });

  describe('错误分析', () => {
    it('should classify webpack config errors correctly', () => {
      const errorOutput = 'ValidationError: Invalid configuration object. Webpack has been initialized';
      const errorType = agent.classifyError(errorOutput);
      
      expect(errorType).toBe('webpack_config');
    });

    it('should classify module resolution errors correctly', () => {
      const errorOutput = 'Module not found: Error: Cannot resolve module "element-ui"';
      const errorType = agent.classifyError(errorOutput);
      
      expect(errorType).toBe('module_resolution');
    });

    it('should extract file paths from error output', () => {
      const errorOutput = `
        Error in src/components/HelloWorld.vue
        Module not found in src/utils/helper.js
        Failed to compile vue.config.js
      `;
      
      const filePaths = agent.extractFilePathsFromError(errorOutput);
      
      expect(filePaths).toContain('src/components/HelloWorld.vue');
      expect(filePaths).toContain('src/utils/helper.js');
      expect(filePaths).toContain('vue.config.js');
    });

    it('should analyze error with tool correctly', async () => {
      const errorOutput = 'Error: Cannot find module "element-ui" in src/main.js';
      
      const result = await agent.executeAgentTool('analyze_error', {
        error_output: errorOutput,
        context: 'build_command'
      });

      expect(result.success).toBe(true);
      expect(result.result.type).toBe('module_resolution');
      expect(result.result.context).toBe('build_command');
      expect(result.result.affectedFiles).toContain('src/main.js');
    });
  });

  describe('主要功能', () => {
    it('should have fixBuildErrors method', () => {
      expect(typeof agent.fixBuildErrors).toBe('function');
    });

    it('should have analyzeProjectContext method', () => {
      expect(typeof agent.analyzeProjectContext).toBe('function');
    });

    it('should have performBuildWithAnalysis method', () => {
      expect(typeof agent.performBuildWithAnalysis).toBe('function');
    });

    it('should maintain agent state correctly', () => {
      expect(agent.agentState.currentTask).toBeNull();
      expect(agent.agentState.conversationHistory).toEqual([]);
      expect(agent.agentState.fixAttempts).toBe(0);
    });
  });
});
