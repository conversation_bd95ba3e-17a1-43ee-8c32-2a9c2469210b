const BuildFixAgent = require('../src/build/buildFixer');

// Mock environment variables for testing
process.env.DEEPSEEK_TOKEN = 'test-token';

describe('BuildFixAgent', () => {
  let buildFixAgent;

  beforeEach(() => {
    buildFixAgent = new BuildFixAgent('/test/path', {
      buildCommand: 'npm run build',
      maxRetries: 2
    });
  });

  it('should construct with correct options', () => {
    expect(buildFixAgent.projectPath).toBe('/test/path');
    expect(buildFixAgent.options.buildCommand).toBe('npm run build');
    expect(buildFixAgent.options.maxRetries).toBe(2);
  });

  it('should initialize agent tools correctly', () => {
    expect(buildFixAgent.agentTools).toBeDefined();
    expect(buildFixAgent.agentTools.read_file).toBeDefined();
    expect(buildFixAgent.agentTools.write_file).toBeDefined();
    expect(buildFixAgent.agentTools.list_files).toBeDefined();
    expect(buildFixAgent.agentTools.analyze_error).toBeDefined();
  });

  it('should execute agent tools correctly', async () => {
    // Mock file system
    const mockReadFile = jest.spyOn(buildFixAgent, 'readFile').mockResolvedValue({
      success: true,
      content: 'test content'
    });

    const result = await buildFixAgent.executeAgentTool('read_file', { file_path: 'test.js' });

    expect(result.success).toBe(true);
    expect(result.content).toBe('test content');
    expect(mockReadFile).toHaveBeenCalledWith('test.js');

    mockReadFile.mockRestore();
  });

  it('should generate analysis prompt correctly', () => {
    const prompt = buildFixAgent.generateAnalysisPrompt('Error: Cannot find module');
    expect(prompt).toContain('AI Agent');
    expect(prompt).toContain('语义化理解');
    expect(prompt).toContain('agent_analysis');
  });

  it('should validate repaired content correctly', () => {
    const valid = buildFixAgent.validateRepairedContent(
      'export default { name: "TestFixed" }',
      'export default { name: "Test" }'
    );
    expect(valid).toBe(true);
  });

  it('should parse agent analysis response correctly', () => {
    const response = `
      <agent_analysis>
        <files_to_fix>
          <file>src/test.vue</file>
          <file>src/config.js</file>
        </files_to_fix>
        <semantic_reasoning>
          Test reasoning
        </semantic_reasoning>
        <fix_strategy>webpack_config</fix_strategy>
      </agent_analysis>
    `;

    const files = buildFixAgent.parseAnalysisResponse(response);
    expect(files).toContain('src/test.vue');
    expect(files).toContain('src/config.js');
  });

  it('should get build stats correctly', () => {
    expect(buildFixAgent.buildStats).toBeDefined();
    expect(buildFixAgent.buildStats.buildAttempts).toBe(0);
    expect(buildFixAgent.buildStats.errorsFixed).toBe(0);
  });
});
