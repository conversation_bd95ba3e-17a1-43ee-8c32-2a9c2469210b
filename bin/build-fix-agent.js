#!/usr/bin/env node

require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const ora = require('ora');
const BuildFixAgent = require('../src/build/buildFixAgent');

const program = new Command();

program
  .name('build-fix-agent')
  .description('🤖 AI Agent 驱动的构建错误修复工具')
  .version('2.0.0');

program
  .command('fix [project-path]')
  .description('🤖 使用 AI Agent 智能修复项目构建错误')
  .option('-c, --build-command <cmd>', '构建命令', 'npm run build')
  .option('-d, --dev-command <cmd>', 'Dev 命令', 'npm run dev')
  .option('-i, --install-command <cmd>', '安装命令', 'npm install')
  .option('-m, --max-attempts <num>', '最大修复尝试次数', '6')
  .option('--mode <mode>', '运行模式: dev | build', 'build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, options) => {
    const spinner = ora('初始化 AI Agent...').start();

    try {
      // 确定项目路径
      const targetPath = projectPath ? path.resolve(projectPath) : process.cwd();

      // 验证项目路径
      if (!await fs.pathExists(targetPath)) {
        throw new Error(`项目路径不存在: ${targetPath}`);
      }

      // 验证是否为有效的 Node.js 项目
      const packageJsonPath = path.join(targetPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error(`未找到 package.json 文件: ${packageJsonPath}`);
      }

      spinner.succeed('项目验证通过');

      // 设置环境变量以控制详细输出
      if (options.verbose) {
        process.env.VERBOSE = 'true';
      }

      // 配置 Agent 选项
      const agentOptions = {
        buildCommand: options.buildCommand,
        devCommand: options.devCommand,
        installCommand: options.installCommand,
        maxAttempts: parseInt(options.maxAttempts),
        mode: options.mode,
        dryRun: options.dryRun,
        verbose: options.verbose
      };

      console.log(chalk.blue('\n🤖 启动 AI Agent 构建修复...'));
      console.log(chalk.gray(`📁 项目路径: ${targetPath}`));
      console.log(chalk.gray(`🎯 运行模式: ${agentOptions.mode}`));
      console.log(chalk.gray(`🏗️  构建命令: ${agentOptions.buildCommand}`));
      console.log(chalk.gray(`🔄 最大尝试次数: ${agentOptions.maxAttempts}`));

      if (agentOptions.dryRun) {
        console.log(chalk.yellow('🔍 运行在预览模式，不会修改文件'));
      }

      // 创建 BuildFixAgent 实例
      const agent = new BuildFixAgent(targetPath, agentOptions);

      // 执行 AI Agent 修复
      const result = await agent.fixBuildErrors();

      // 显示结果
      displayAgentResults(result, agentOptions);

    } catch (error) {
      spinner.fail('AI Agent 执行失败');
      console.error(chalk.red('\n❌ 错误:'), error.message);

      if (options.verbose) {
        console.error(chalk.gray('\n详细错误信息:'));
        console.error(chalk.gray(error.stack));
      }

      process.exit(1);
    }
  });

program
  .command('analyze [project-path]')
  .description('🔍 分析项目结构和潜在问题')
  .option('--focus <type>', '分析重点: structure | dependencies | config | all', 'all')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, options) => {
    try {
      const targetPath = projectPath ? path.resolve(projectPath) : process.cwd();
      const agent = new BuildFixAgent(targetPath, { verbose: options.verbose });

      console.log(chalk.blue('\n🔍 AI Agent 项目分析...'));
      
      const result = await agent.executeAgentTool('analyze_project', { 
        focus: options.focus 
      });

      if (result.success) {
        console.log(chalk.green('\n✅ 项目分析完成'));
        console.log(chalk.blue('\n📋 分析结果:'));
        console.log(JSON.stringify(result.result, null, 2));
      } else {
        console.error(chalk.red('\n❌ 项目分析失败:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ 分析失败:'), error.message);
      process.exit(1);
    }
  });

program
  .command('test-tools')
  .description('🧪 测试 AI Agent 工具系统')
  .option('--project-path <path>', '项目路径', process.cwd())
  .action(async (options) => {
    try {
      const agent = new BuildFixAgent(options.projectPath, { verbose: true });

      console.log(chalk.blue('\n🧪 测试 AI Agent 工具系统...'));
      
      // 测试项目分析工具
      console.log(chalk.gray('\n1. 测试项目分析工具...'));
      const analysisResult = await agent.executeAgentTool('analyze_project', { focus: 'all' });
      console.log(analysisResult.success ? chalk.green('✅ 项目分析工具正常') : chalk.red('❌ 项目分析工具失败'));

      // 测试文件列表工具
      console.log(chalk.gray('\n2. 测试文件列表工具...'));
      const listResult = await agent.executeAgentTool('list_files', { directory: '.' });
      console.log(listResult.success ? chalk.green('✅ 文件列表工具正常') : chalk.red('❌ 文件列表工具失败'));

      // 测试错误分析工具
      console.log(chalk.gray('\n3. 测试错误分析工具...'));
      const errorResult = await agent.executeAgentTool('analyze_error', { 
        error_output: 'Error: Cannot find module "test-module"' 
      });
      console.log(errorResult.success ? chalk.green('✅ 错误分析工具正常') : chalk.red('❌ 错误分析工具失败'));

      console.log(chalk.green('\n🎉 AI Agent 工具系统测试完成'));
    } catch (error) {
      console.error(chalk.red('❌ 工具测试失败:'), error.message);
      process.exit(1);
    }
  });

/**
 * 显示 AI Agent 修复结果
 */
function displayAgentResults(result, options) {
  console.log(chalk.blue('\n🤖 AI Agent 修复结果:'));

  // 状态显示
  const statusIcon = result.success ? '🎉' : '⚠️';
  const statusColor = result.success ? chalk.green : chalk.yellow;
  console.log(`${statusIcon} 状态: ${statusColor(result.success ? 'AI Agent 修复成功' : 'AI Agent 修复未完成')}`);

  // 详细信息
  if (result.message) {
    console.log(chalk.gray(`📝 详细信息: ${result.message}`));
  }

  if (result.attempts !== undefined) {
    console.log(chalk.gray(`🔄 修复尝试次数: ${result.attempts}`));
  }

  // 成功情况的建议
  if (result.success) {
    console.log(chalk.green('\n🎉 恭喜！AI Agent 成功修复了构建错误！'));
    console.log(chalk.blue('\n📋 后续建议:'));
    console.log(chalk.gray('  ✓ 运行测试确保功能正常'));
    console.log(chalk.gray('  ✓ 检查 AI Agent 修复的代码'));
    console.log(chalk.gray('  ✓ 提交代码变更'));
    console.log(chalk.gray('  ✓ 更新项目文档'));
  } else {
    console.log(chalk.yellow('\n⚠️  AI Agent 修复未完全成功'));
    console.log(chalk.blue('\n🔧 建议操作:'));
    console.log(chalk.gray('  1. 查看 AI Agent 日志了解详情'));
    console.log(chalk.gray('  2. 尝试使用 --verbose 查看详细信息'));
    console.log(chalk.gray('  3. 手动检查和修复复杂问题'));
    console.log(chalk.gray('  4. 考虑分步骤进行修复'));
  }

  console.log(chalk.blue('\n❓ 需要帮助?'));
  console.log(chalk.gray('  • 运行 build-fix-agent --help 查看所有选项'));
  console.log(chalk.gray('  • 使用 build-fix-agent analyze 分析项目'));
  console.log(chalk.gray('  • 使用 build-fix-agent test-tools 测试工具'));
}

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
  process.exit(1);
});

// 解析命令行参数
program.parse();

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
