const { AIService } = require('../ai/ai-service');
const chalk = require('chalk');
const path = require('path');

/**
 * BuildFixAgent - 新一代 AI 驱动的构建错误修复代理
 * 
 * 核心设计理念：
 * 1. 语义化理解 - 基于语义而非规则的错误分析
 * 2. Agent 架构 - 类似 Augment Agent 的工具调用机制
 * 3. 智能决策 - AI 驱动的修复策略选择
 * 4. 迭代修复 - 支持多轮对话式问题解决
 * 
 * 与传统 BuildFixer 的区别：
 * - 更强的语义理解能力
 * - 更灵活的工具调用机制
 * - 更智能的错误分析
 * - 更好的可扩展性
 */
class BuildFixAgent extends AIService {
  constructor(projectPath, options = {}) {
    super({
      ...options,
      logDir: options.logDir || path.join(projectPath, 'ai-logs')
    });

    this.projectPath = projectPath;
    this.options = {
      buildCommand: 'npm run build',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      maxAttempts: 6,
      mode: 'build',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      interactive: false,
      explain: false,
      verbose: false,
      ...options
    };

    // Agent 状态管理
    this.agentState = {
      currentTask: null,
      conversationHistory: [],
      fixAttempts: 0,
      lastError: null,
      strategy: null
    };

    // 初始化 Agent 工具系统
    this.agentTools = this.initializeAgentTools();
    
    console.log(chalk.blue('🤖 BuildFixAgent 已初始化'));
    console.log(chalk.gray(`   项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`   AI 服务: ${this.isEnabled() ? '启用' : '禁用'}`));
  }

  /**
   * 初始化 Agent 工具系统
   */
  initializeAgentTools() {
    return {
      analyze_project: {
        description: '分析项目结构和配置，了解项目的技术栈和架构',
        parameters: {
          type: 'object',
          properties: {
            focus: {
              type: 'string',
              description: '分析重点：structure | dependencies | config | all'
            }
          }
        },
        handler: this.toolAnalyzeProject.bind(this)
      },

      read_file: {
        description: '读取项目文件内容，用于理解代码结构和问题',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            }
          },
          required: ['file_path']
        },
        handler: this.toolReadFile.bind(this)
      },

      write_file: {
        description: '写入或修改文件，实施修复方案',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            },
            content: {
              type: 'string',
              description: '要写入的完整文件内容'
            },
            backup: {
              type: 'boolean',
              description: '是否创建备份文件'
            }
          },
          required: ['file_path', 'content']
        },
        handler: this.toolWriteFile.bind(this)
      },

      list_files: {
        description: '列出目录中的文件，了解项目结构',
        parameters: {
          type: 'object',
          properties: {
            directory: {
              type: 'string',
              description: '要列出的目录路径'
            },
            pattern: {
              type: 'string',
              description: '文件匹配模式'
            }
          },
          required: ['directory']
        },
        handler: this.toolListFiles.bind(this)
      },

      execute_command: {
        description: '执行构建命令，验证修复效果',
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'string',
              description: '要执行的命令'
            },
            timeout: {
              type: 'number',
              description: '超时时间（毫秒）'
            }
          },
          required: ['command']
        },
        handler: this.toolExecuteCommand.bind(this)
      },

      analyze_error: {
        description: '深度分析构建错误，提供语义化的错误理解',
        parameters: {
          type: 'object',
          properties: {
            error_output: {
              type: 'string',
              description: '构建错误输出'
            },
            context: {
              type: 'string',
              description: '错误上下文信息'
            }
          },
          required: ['error_output']
        },
        handler: this.toolAnalyzeError.bind(this)
      }
    };
  }

  /**
   * Agent 主要入口：智能修复构建错误
   */
  async fixBuildErrors() {
    console.log(chalk.blue('\n🤖 BuildFixAgent 开始智能修复...'));
    
    this.agentState.currentTask = 'build_fix';
    this.agentState.fixAttempts = 0;

    try {
      // 第一步：分析项目
      await this.analyzeProjectContext();
      
      // 第二步：执行构建并分析错误
      const buildResult = await this.performBuildWithAnalysis();
      
      if (buildResult.success) {
        console.log(chalk.green('🎉 项目构建成功，无需修复！'));
        return { success: true, message: '构建成功' };
      }

      // 第三步：AI Agent 智能修复
      return await this.performAgentFix(buildResult.error);
      
    } catch (error) {
      console.error(chalk.red('❌ BuildFixAgent 执行失败:'), error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 分析项目上下文
   */
  async analyzeProjectContext() {
    console.log(chalk.blue('📋 分析项目上下文...'));
    
    const analysis = await this.executeAgentTool('analyze_project', { focus: 'all' });
    
    if (analysis.success) {
      this.agentState.conversationHistory.push({
        type: 'project_analysis',
        data: analysis.result
      });
      console.log(chalk.green('✅ 项目分析完成'));
    } else {
      console.log(chalk.yellow('⚠️  项目分析失败，继续执行'));
    }
  }

  /**
   * 执行构建并分析错误
   */
  async performBuildWithAnalysis() {
    console.log(chalk.blue('🔨 执行构建命令...'));
    
    const buildResult = await this.executeAgentTool('execute_command', {
      command: this.options.buildCommand,
      timeout: 60000
    });

    if (!buildResult.success) {
      // 分析构建错误
      const errorAnalysis = await this.executeAgentTool('analyze_error', {
        error_output: buildResult.output || buildResult.error,
        context: 'build_command'
      });

      return {
        success: false,
        error: buildResult.output || buildResult.error,
        analysis: errorAnalysis.success ? errorAnalysis.result : null
      };
    }

    return { success: true };
  }

  /**
   * 执行 Agent 工具
   */
  async executeAgentTool(toolName, parameters) {
    const tool = this.agentTools[toolName];
    if (!tool) {
      return { success: false, error: `未知工具: ${toolName}` };
    }

    try {
      console.log(chalk.gray(`  🔧 执行工具: ${toolName}`));
      const result = await tool.handler(parameters);
      
      if (result.success) {
        console.log(chalk.green(`  ✅ 工具执行成功: ${toolName}`));
      } else {
        console.log(chalk.yellow(`  ⚠️  工具执行失败: ${toolName}`));
      }
      
      return result;
    } catch (error) {
      console.log(chalk.red(`  ❌ 工具执行异常: ${toolName} - ${error.message}`));
      return { success: false, error: error.message };
    }
  }

  /**
   * 执行 AI Agent 智能修复
   */
  async performAgentFix(errorOutput) {
    console.log(chalk.blue('🧠 AI Agent 开始智能分析和修复...'));

    // 这里将实现更复杂的 AI Agent 逻辑
    // 包括多轮对话、策略选择、迭代修复等

    return {
      success: false,
      message: 'AI Agent 修复逻辑待实现',
      attempts: this.agentState.fixAttempts
    };
  }

  // ==================== Agent 工具实现 ====================

  /**
   * 工具：分析项目
   */
  async toolAnalyzeProject(parameters) {
    try {
      const fs = require('fs-extra');
      const focus = parameters.focus || 'all';

      const analysis = {
        structure: null,
        dependencies: null,
        config: null
      };

      if (focus === 'all' || focus === 'structure') {
        // 分析项目结构
        const srcExists = await fs.pathExists(path.join(this.projectPath, 'src'));
        const publicExists = await fs.pathExists(path.join(this.projectPath, 'public'));

        analysis.structure = {
          hasSrcDir: srcExists,
          hasPublicDir: publicExists,
          type: srcExists ? 'vue_project' : 'unknown'
        };
      }

      if (focus === 'all' || focus === 'dependencies') {
        // 分析依赖
        const packageJsonPath = path.join(this.projectPath, 'package.json');
        if (await fs.pathExists(packageJsonPath)) {
          const packageJson = await fs.readJson(packageJsonPath);
          analysis.dependencies = {
            vue: packageJson.dependencies?.vue || packageJson.devDependencies?.vue,
            elementUI: packageJson.dependencies?.['element-ui'] || packageJson.dependencies?.['element-plus'],
            buildTool: packageJson.devDependencies?.['@vue/cli-service'] ? 'vue-cli' : 'unknown'
          };
        }
      }

      if (focus === 'all' || focus === 'config') {
        // 分析配置文件
        const vueConfigExists = await fs.pathExists(path.join(this.projectPath, 'vue.config.js'));
        const webpackConfigExists = await fs.pathExists(path.join(this.projectPath, 'webpack.config.js'));

        analysis.config = {
          hasVueConfig: vueConfigExists,
          hasWebpackConfig: webpackConfigExists
        };
      }

      return {
        success: true,
        result: analysis
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 工具：读取文件
   */
  async toolReadFile(parameters) {
    try {
      const fs = require('fs-extra');
      const filePath = path.join(this.projectPath, parameters.file_path);

      if (!await fs.pathExists(filePath)) {
        return {
          success: false,
          error: '文件不存在'
        };
      }

      const content = await fs.readFile(filePath, 'utf8');
      return {
        success: true,
        content,
        size: content.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 工具：写入文件
   */
  async toolWriteFile(parameters) {
    try {
      const fs = require('fs-extra');
      const filePath = path.join(this.projectPath, parameters.file_path);

      // 创建备份
      if (parameters.backup !== false && await fs.pathExists(filePath)) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await fs.copy(filePath, backupPath);
        console.log(chalk.gray(`    📁 已创建备份: ${path.basename(backupPath)}`));
      }

      // 确保目录存在
      await fs.ensureDir(path.dirname(filePath));

      // 写入文件
      await fs.writeFile(filePath, parameters.content, 'utf8');

      return {
        success: true,
        message: '文件写入成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 工具：列出文件
   */
  async toolListFiles(parameters) {
    try {
      const fs = require('fs-extra');
      const dirPath = path.join(this.projectPath, parameters.directory);

      if (!await fs.pathExists(dirPath)) {
        return {
          success: false,
          error: '目录不存在'
        };
      }

      let files = await fs.readdir(dirPath);

      if (parameters.pattern) {
        const minimatch = require('minimatch');
        files = files.filter(file => minimatch(file, parameters.pattern));
      }

      return {
        success: true,
        files,
        count: files.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 工具：执行命令
   */
  async toolExecuteCommand(parameters) {
    try {
      const { execSync } = require('child_process');
      const timeout = parameters.timeout || 30000;

      const output = execSync(parameters.command, {
        cwd: this.projectPath,
        encoding: 'utf8',
        timeout: timeout,
        stdio: 'pipe'
      });

      return {
        success: true,
        output,
        command: parameters.command
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        output: error.stdout || error.stderr || '',
        command: parameters.command
      };
    }
  }

  /**
   * 工具：分析错误
   */
  async toolAnalyzeError(parameters) {
    try {
      const errorOutput = parameters.error_output;
      const context = parameters.context || 'unknown';

      // 提取关键错误信息
      const errorLines = errorOutput.split('\n').filter(line => {
        const lowerLine = line.toLowerCase();
        return lowerLine.includes('error') ||
               lowerLine.includes('failed') ||
               lowerLine.includes('cannot find') ||
               lowerLine.includes('module not found') ||
               lowerLine.includes('syntax error') ||
               lowerLine.includes('validationerror');
      });

      // 分析错误类型
      const errorType = this.classifyError(errorOutput);

      // 提取文件路径
      const filePaths = this.extractFilePathsFromError(errorOutput);

      return {
        success: true,
        result: {
          type: errorType,
          keyErrors: errorLines.slice(0, 5),
          affectedFiles: filePaths,
          context: context,
          severity: errorLines.length > 10 ? 'high' : 'medium'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 分类错误类型
   */
  classifyError(errorOutput) {
    const output = errorOutput.toLowerCase();

    if (output.includes('webpack') || output.includes('configuration')) {
      return 'webpack_config';
    } else if (output.includes('module not found') || output.includes('cannot resolve') || output.includes('cannot find module')) {
      return 'module_resolution';
    } else if (output.includes('syntax error') || output.includes('unexpected token')) {
      return 'syntax_error';
    } else if (output.includes('vue') && output.includes('migration')) {
      return 'vue_migration';
    } else {
      return 'unknown';
    }
  }

  /**
   * 从错误输出中提取文件路径
   */
  extractFilePathsFromError(errorOutput) {
    const lines = errorOutput.split('\n');
    const filePaths = [];

    for (const line of lines) {
      // 匹配常见的文件路径模式
      const matches = line.match(/(?:src\/|\.\/)?[\w/\-.]+\.(vue|js|ts|jsx|tsx|json)/g);
      if (matches) {
        filePaths.push(...matches);
      }
    }

    return [...new Set(filePaths)]; // 去重
  }
}

module.exports = BuildFixAgent;
