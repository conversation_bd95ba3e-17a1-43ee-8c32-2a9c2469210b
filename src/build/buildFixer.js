const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const { AIService } = require('../ai/ai-service');
const ConfigLoader = require('./configLoader');

/**
 * BuildFixAgent - AI 驱动的构建错误修复代理
 *
 * 采用 AI Agent 架构，类似 Augment Agent 的工具调用方式：
 * 1. 智能错误分析 - 无需预定义错误分类
 * 2. 工具驱动修复 - 通过工具调用实现文件操作
 * 3. 多轮对话修复 - 支持迭代式问题解决
 * 4. 语义化理解 - 基于语义而非规则的修复策略
 */

class BuildFixAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath, 'ai-logs')
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.userOptions = options;
    // 不要重置 this.options，它包含了 AIService 的配置

    // 配置加载器
    this.configLoader = new ConfigLoader();

    // 构建统计
    this.buildStats = {
      startTime: null,
      endTime: null,
      duration: 0,
      buildAttempts: 0,
      buildSuccess: false,
      finalBuildSuccess: false,
      errorsFound: [],
      errorsFixed: 0
    };

    // 重复检测
    this.attemptHistory = {
      filesToFix: [], // 记录每次尝试修复的文件列表
      errorHashes: [], // 记录每次的错误哈希
      lastErrorOutput: null // 记录上次的错误输出
    };

    // Spinner 状态
    this.spinner = null;
    this.interactiveMode = options.interactive || false;

    // AI Agent 工具系统 - 类似 Augment Agent 的函数调用机制
    this.agentTools = this.initializeAgentTools();
  }

  /**
   * 初始化 AI Agent 工具系统
   * 类似 Augment Agent 的工具定义，支持函数调用
   */
  initializeAgentTools() {
    return {
      // 文件系统工具
      read_file: {
        description: '读取项目中的文件内容，用于分析代码结构和错误',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            }
          },
          required: ['file_path']
        },
        handler: this.toolReadFile.bind(this)
      },

      write_file: {
        description: '写入或修改项目中的文件，用于修复代码错误',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: '相对于项目根目录的文件路径'
            },
            content: {
              type: 'string',
              description: '要写入的完整文件内容'
            }
          },
          required: ['file_path', 'content']
        },
        handler: this.toolWriteFile.bind(this)
      },

      list_files: {
        description: '列出项目目录中的文件，用于了解项目结构',
        parameters: {
          type: 'object',
          properties: {
            directory: {
              type: 'string',
              description: '要列出的目录路径，相对于项目根目录'
            },
            pattern: {
              type: 'string',
              description: '文件匹配模式，如 "*.vue" 或 "*.js"'
            }
          },
          required: ['directory']
        },
        handler: this.toolListFiles.bind(this)
      },

      analyze_error: {
        description: '分析构建错误，提取关键信息和可能的解决方案',
        parameters: {
          type: 'object',
          properties: {
            error_output: {
              type: 'string',
              description: '构建错误的完整输出'
            }
          },
          required: ['error_output']
        },
        handler: this.toolAnalyzeError.bind(this)
      }
    };
  }

  // Spinner 管理方法
  startSpinner(text) {
    if (this.spinner) {
      this.spinner.stop();
    }
    this.spinner = ora(text).start();
  }

  updateSpinner(text) {
    if (this.spinner) {
      this.spinner.text = text;
    }
  }

  succeedSpinner(text) {
    if (this.spinner) {
      this.spinner.succeed(text);
      this.spinner = null;
    }
  }

  failSpinner(text) {
    if (this.spinner) {
      this.spinner.fail(text);
      this.spinner = null;
    }
  }

  stopSpinner() {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }

  /**
   * 执行构建并修复错误 - 主入口方法
   */
  async buildAndFix() {
    this.buildStats.startTime = Date.now();

    try {
      this.startSpinner('开始构建项目并修复错误...');

      // 初始化
      await this.initialize();

      // 根据模式执行不同的构建策略
      let buildResult;
      if (this.options.mode === 'dev') {
        this.updateSpinner('执行 dev 模式错误检测...');
        buildResult = await this.performDevCheck();
      } else {
        this.updateSpinner('执行构建...');
        buildResult = await this.performBuild();
      }

      if (buildResult.success) {
        this.succeedSpinner(`${this.options.mode === 'dev' ? 'Dev 检测' : '项目构建'}成功！`);
        this.buildStats.buildSuccess = true;
        this.buildStats.finalBuildSuccess = true;
        return this.createResult(true);
      }

      // 使用 AI Agent 分析和修复错误
      this.updateSpinner('AI Agent 正在分析构建错误...');
      const fixResult = await this.analyzeAndFixWithAI(buildResult);

      this.stopSpinner();
      this.printBuildStats();
      return fixResult;
    } catch (error) {
      this.failSpinner('构建修复过程失败');
      console.error(chalk.red('❌ 错误详情:'), error.message);
      throw error;
    } finally {
      this.buildStats.endTime = Date.now();
      this.buildStats.duration = this.buildStats.endTime - this.buildStats.startTime;
      this.stopSpinner();
    }
  }

  /**
   * 初始化构建修复器
   */
  async initialize() {
    const configFileOptions = await this.configLoader.loadConfig(
      this.userOptions.configPath,
      this.projectPath
    );

    // 合并配置：用户选项 > 配置文件 > 默认值
    // 保留 AIService 的配置（如 logDir）
    const aiServiceOptions = {
      logDir: this.options.logDir,
      maxTokens: this.options.maxTokens,
      temperature: this.options.temperature,
      maxRetries: this.options.maxRetries
    };

    console.log(chalk.gray(`🔍 aiServiceOptions.logDir: ${aiServiceOptions.logDir}`));

    this.options = {
      buildCommand: 'npm run build',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      maxAttempts: 6,
      mode: 'build',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      interactive: false,
      explain: false,
      verbose: false,
      ...aiServiceOptions, // 保留 AIService 配置
      ...configFileOptions,
      ...this.userOptions
    };

    console.log(chalk.blue('🔧 构建修复器配置:'));
    console.log(chalk.gray(`  项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`  构建命令: ${this.options.buildCommand}`));
    console.log(chalk.gray(`  运行模式: ${this.options.mode}`));
    console.log(chalk.gray(`  最大尝试: ${this.options.maxAttempts}`));
    console.log(chalk.gray(`  AI 修复: ${this.options.skipAI ? '禁用' : '启用'}`));
  }

  /**
   * 使用 AI Agent 分析和修复错误
   */
  async analyzeAndFixWithAI(buildResult) {
    if (this.options.skipAI) {
      console.log(chalk.yellow('⚠️  跳过 AI 修复步骤'));
      return this.createResult(false, 'AI 修复被跳过');
    }

    if (!this.isEnabled()) {
      console.log(chalk.yellow('⚠️  AI 服务不可用，无法进行智能修复'));
      return this.createResult(false, 'AI 服务不可用');
    }

    // 收集构建错误信息
    let buildOutput = buildResult.output || '';
    console.log(chalk.blue('\n🤖 AI Agent 开始分析构建错误...'));

    // 尝试修复错误
    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxAttempts}...`));

      const fixResult = await this.performAIFix(buildOutput, attempt);

      if (fixResult.filesModified > 0) {
        console.log(chalk.green(`✅ AI Agent 修改了 ${fixResult.filesModified} 个文件`));
        this.buildStats.errorsFixed += fixResult.filesModified;

        // 重新构建验证修复效果
        console.log(chalk.blue('\n🔄 重新构建项目验证修复效果...'));
        const newBuildResult = await this.performBuild();

        if (newBuildResult.success) {
          console.log(chalk.green('🎉 构建成功！所有错误已修复'));
          this.buildStats.finalBuildSuccess = true;
          this.displaySuccessMessage();
          return this.createResult(true);
        } else {
          console.log(chalk.yellow('⚠️  构建仍有问题，准备下一轮修复...'));
          buildOutput = newBuildResult.output;
        }
      } else {
        console.log(chalk.yellow('⚠️  本轮 AI Agent 未能修复任何文件'));
      }
    }

    console.log(chalk.red(`❌ 经过 ${this.options.maxAttempts} 次尝试，仍无法完全修复构建错误`));
    return this.createResult(false, `AI 修复未能完全解决问题，已尝试 ${this.options.maxAttempts} 次`);
  }

  /**
   * 执行 AI 修复 - 两阶段过程
   */
  async performAIFix(buildOutput) {
    try {
      // 检查是否重复尝试相同的修复
      if (this.isRepeatingAttempt(buildOutput)) {
        console.log(chalk.yellow('  ⚠️  检测到重复尝试，切换修复策略...'));
        return await this.performAlternativeStrategy(buildOutput);
      }

      // 第一阶段：让 AI 分析错误并选择要查看的文件
      console.log(chalk.gray('  📋 阶段1: AI 分析错误并选择相关文件...'));
      const analysisResult = await this.analyzeErrorsWithAI(buildOutput);

      if (!analysisResult.success) {
        console.log(chalk.yellow('  ⚠️  AI 错误分析失败'));
        return { filesModified: 0, error: analysisResult.error };
      }

      // 记录本次尝试的文件列表
      this.recordAttempt(buildOutput, analysisResult.filesToFix);

      // 第二阶段：让 AI 修复选定的文件
      console.log(chalk.gray('  🔧 阶段2: AI 修复相关文件...'));
      const fixResult = await this.fixFilesWithAI(analysisResult.filesToFix, buildOutput);

      return fixResult;
    } catch (error) {
      console.error(chalk.red('  ❌ AI 修复过程异常:'), error.message);
      return { filesModified: 0, error: error.message };
    }
  }

  /**
   * 第一阶段：AI 分析错误并选择相关文件
   */
  async analyzeErrorsWithAI(buildOutput) {
    try {
      // 只在 verbose 模式下显示详细信息
      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 构建错误输出长度: ${buildOutput.length} 字符`));
        console.log(chalk.gray(`    🔍 AI logDir: ${this.options.logDir}`));
      }

      const prompt = this.generateAnalysisPrompt(buildOutput);

      if (this.options.verbose) {
        console.log(chalk.gray(`    📝 生成的提示词长度: ${prompt.length} 字符`));
      }

      const response = await this.callAI(prompt);

      if (this.options.verbose) {
        console.log(chalk.gray(`    🤖 AI 响应长度: ${response.length} 字符`));
        console.log(chalk.gray(`    📋 AI 响应内容预览: ${response.substring(0, 200)}...`));
      }

      // 解析 AI 响应，提取要修复的文件列表
      const filesToFix = this.parseAnalysisResponse(response);

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 解析出的文件数量: ${filesToFix.length}`));
      }

      if (filesToFix.length === 0) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  AI 响应解析结果为空，原始响应: ${response.substring(0, 500)}...`));
        }
        return {
          success: false,
          error: 'AI 未能识别需要修复的文件'
        };
      }

      console.log(chalk.gray(`    ✅ AI 识别出 ${filesToFix.length} 个需要修复的文件`));
      filesToFix.forEach(file => {
        console.log(chalk.gray(`      - ${file}`));
      });

      return {
        success: true,
        filesToFix
      };
    } catch (error) {
      console.log(chalk.red(`    ❌ AI 分析异常: ${error.message}`));
      if (this.options.verbose) {
        console.log(chalk.gray(`    📋 错误堆栈: ${error.stack}`));
      }
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 第二阶段：AI 修复选定的文件
   */
  async fixFilesWithAI(filesToFix, buildOutput) {
    let filesModified = 0;
    const errors = [];

    for (const filePath of filesToFix) {
      try {
        console.log(chalk.gray(`    🔧 修复文件: ${filePath}`));

        // 读取文件内容
        const fileContent = await this.executeToolCall('read_file', { file_path: filePath });

        if (!fileContent.success) {
          console.log(chalk.yellow(`      ⚠️  无法读取文件: ${fileContent.error}`));
          errors.push(`无法读取文件 ${filePath}: ${fileContent.error}`);
          continue;
        }

        // 让 AI 修复文件
        const fixResult = await this.fixSingleFileWithAI(filePath, fileContent.content, buildOutput);

        if (fixResult.success) {
          // 写入修复后的文件
          const writeResult = await this.executeToolCall('write_file', {
            file_path: filePath,
            content: fixResult.fixedContent
          });

          if (writeResult.success) {
            console.log(chalk.green('      ✅ 文件修复成功'));
            filesModified++;
          } else {
            console.log(chalk.yellow(`      ⚠️  无法写入文件: ${writeResult.error}`));
            errors.push(`无法写入文件 ${filePath}: ${writeResult.error}`);
          }
        } else {
          console.log(chalk.yellow(`      ⚠️  AI 修复失败: ${fixResult.error}`));
          errors.push(`AI 修复失败 ${filePath}: ${fixResult.error}`);
        }
      } catch (error) {
        console.log(chalk.red(`      ❌ 修复异常: ${error.message}`));
        errors.push(`修复异常 ${filePath}: ${error.message}`);
      }
    }

    return {
      filesModified,
      errors,
      totalFiles: filesToFix.length
    };
  }

  /**
   * AI Agent 工具调用执行器
   * 类似 Augment Agent 的工具调用机制
   */
  async executeAgentTool(toolName, parameters) {
    try {
      const tool = this.agentTools[toolName];
      if (!tool) {
        return {
          success: false,
          error: `未知的 Agent 工具: ${toolName}`
        };
      }

      console.log(chalk.gray(`    🔧 执行 Agent 工具: ${toolName}`));
      const result = await tool.handler(parameters);

      if (result.success) {
        console.log(chalk.green(`    ✅ 工具执行成功: ${toolName}`));
      } else {
        console.log(chalk.yellow(`    ⚠️  工具执行失败: ${toolName} - ${result.error}`));
      }

      return result;
    } catch (error) {
      console.log(chalk.red(`    ❌ 工具执行异常: ${toolName} - ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 兼容性方法：执行工具调用（保持向后兼容）
   */
  async executeToolCall(toolName, parameters) {
    return await this.executeAgentTool(toolName, parameters);
  }

  /**
   * Agent 工具：读取文件
   */
  async toolReadFile(parameters) {
    return await this.readFile(parameters.file_path);
  }

  /**
   * Agent 工具：写入文件
   */
  async toolWriteFile(parameters) {
    return await this.writeFile(parameters.file_path, parameters.content);
  }

  /**
   * Agent 工具：列出文件
   */
  async toolListFiles(parameters) {
    return await this.listFiles(parameters.directory, parameters.pattern);
  }

  /**
   * Agent 工具：分析错误
   */
  async toolAnalyzeError(parameters) {
    try {
      const errorOutput = parameters.error_output;

      // 提取关键错误信息
      const errorLines = errorOutput.split('\n').filter(line => {
        const lowerLine = line.toLowerCase();
        return lowerLine.includes('error') ||
               lowerLine.includes('failed') ||
               lowerLine.includes('cannot find') ||
               lowerLine.includes('module not found') ||
               lowerLine.includes('syntax error') ||
               lowerLine.includes('validationerror');
      });

      // 分析错误类型
      const errorAnalysis = this.analyzeErrorType(errorOutput);

      return {
        success: true,
        analysis: {
          errorLines: errorLines.slice(0, 5), // 只返回前5行关键错误
          errorType: errorAnalysis,
          summary: `发现 ${errorLines.length} 个错误信息`
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 读取文件工具（底层实现）
   */
  async readFile(filePath) {
    try {
      const fullPath = path.join(this.projectPath, filePath);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '文件不存在'
        };
      }

      const content = await fs.readFile(fullPath, 'utf8');
      return {
        success: true,
        content
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 写入文件工具
   */
  async writeFile(filePath, content) {
    try {
      if (this.options.dryRun) {
        console.log(chalk.gray(`      [预览模式] 将写入文件: ${filePath}`));
        return {
          success: true,
          message: '预览模式，未实际写入'
        };
      }

      const fullPath = path.join(this.projectPath, filePath);

      // 确保目录存在
      await fs.ensureDir(path.dirname(fullPath));

      // 备份原文件
      if (await fs.pathExists(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        await fs.copy(fullPath, backupPath);
      }

      await fs.writeFile(fullPath, content, 'utf8');
      return {
        success: true,
        message: '文件写入成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 列出文件工具
   */
  async listFiles(directory, pattern) {
    try {
      const fullPath = path.join(this.projectPath, directory);

      if (!await fs.pathExists(fullPath)) {
        return {
          success: false,
          error: '目录不存在'
        };
      }

      const files = await fs.readdir(fullPath);
      let filteredFiles = files;

      if (pattern) {
        const glob = require('glob');
        filteredFiles = files.filter(file => glob.minimatch(file, pattern));
      }

      return {
        success: true,
        files: filteredFiles
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查是否重复尝试相同的修复
   */
  isRepeatingAttempt(buildOutput) {
    // 生成当前错误的哈希
    const currentErrorHash = this.generateErrorHash(buildOutput);

    // 检查是否已经尝试过相同的错误
    if (this.attemptHistory.errorHashes.includes(currentErrorHash)) {
      return true;
    }

    // 检查错误输出是否与上次完全相同
    if (this.attemptHistory.lastErrorOutput === buildOutput) {
      return true;
    }

    return false;
  }

  /**
   * 生成错误哈希
   */
  generateErrorHash(buildOutput) {
    // 提取关键错误信息
    const errorLines = buildOutput.split('\n').filter(line => {
      const lowerLine = line.toLowerCase();
      return lowerLine.includes('error') ||
             lowerLine.includes('failed') ||
             lowerLine.includes('validationerror');
    });

    // 生成简单哈希
    return errorLines.join('|').replace(/\s+/g, ' ').trim();
  }

  /**
   * 记录本次尝试
   */
  recordAttempt(buildOutput, filesToFix) {
    const errorHash = this.generateErrorHash(buildOutput);

    this.attemptHistory.errorHashes.push(errorHash);
    this.attemptHistory.filesToFix.push([...filesToFix]);
    this.attemptHistory.lastErrorOutput = buildOutput;

    // 只保留最近3次尝试的记录
    if (this.attemptHistory.errorHashes.length > 3) {
      this.attemptHistory.errorHashes.shift();
      this.attemptHistory.filesToFix.shift();
    }
  }

  /**
   * 执行替代修复策略
   */
  async performAlternativeStrategy(buildOutput) {
    console.log(chalk.blue('  🔄 尝试替代修复策略...'));

    // 策略1: 直接删除有问题的配置
    if (buildOutput.includes('config.plugin(\'preload\').use({')) {
      console.log(chalk.gray('    📝 策略: 删除有问题的 preload 插件配置'));
      return await this.removeProblematicConfig(buildOutput);
    }

    // 策略2: 使用更简单的提示词
    console.log(chalk.gray('    📝 策略: 使用简化的修复提示词'));
    return await this.performSimplifiedFix(buildOutput);
  }

  /**
   * 删除有问题的配置
   */
  async removeProblematicConfig() {
    const vueConfigPath = path.join(this.projectPath, 'vue.config.js');

    try {
      if (await fs.pathExists(vueConfigPath)) {
        const content = await fs.readFile(vueConfigPath, 'utf8');

        // 删除有问题的 preload 插件配置
        const fixedContent = content.replace(
          /config\.plugin\('preload'\)\.use\(\{\s*\/\/[^}]*\}\)/g,
          '// Removed problematic preload plugin configuration'
        );

        if (fixedContent !== content) {
          await this.writeFileWithBackup(vueConfigPath, fixedContent);
          console.log(chalk.green('    ✅ 已删除有问题的插件配置'));
          return { filesModified: 1 };
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  替代策略失败: ${error.message}`));
    }

    return { filesModified: 0 };
  }

  /**
   * 执行简化修复
   */
  async performSimplifiedFix() {
    // 使用更直接的方法，针对常见问题进行修复
    const vueConfigPath = path.join(this.projectPath, 'vue.config.js');

    try {
      if (await fs.pathExists(vueConfigPath)) {
        const content = await fs.readFile(vueConfigPath, 'utf8');
        let fixedContent = content;

        // 修复常见的 webpack 配置问题
        fixedContent = this.applyCommonFixes(fixedContent);

        if (fixedContent !== content) {
          await this.writeFileWithBackup(vueConfigPath, fixedContent);
          console.log(chalk.green('    ✅ 应用了常见修复方案'));
          return { filesModified: 1 };
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  简化修复失败: ${error.message}`));
    }

    return { filesModified: 0 };
  }

  /**
   * 应用常见修复方案
   */
  applyCommonFixes(content) {
    let fixed = content;

    // 修复 preload 插件配置问题
    fixed = fixed.replace(
      /config\.plugin\('preload'\)\.use\(\{\s*\/\/[^}]*\}\)/g,
      `config.plugin('preload').tap(options => [
        {
          rel: 'preload',
          fileBlacklist: [/\\.map$/, /hot-update\\.js$/, /runtime\\..*\\.js$/],
          include: 'initial'
        }
      ])`
    );

    // 修复空的插件配置
    fixed = fixed.replace(
      /config\.plugin\([^)]+\)\.use\(\{\s*\}\)/g,
      '// Removed empty plugin configuration'
    );

    return fixed;
  }

  /**
   * 生成 AI Agent 错误分析提示词
   * 采用语义化理解方式，而非规则匹配
   */
  generateAnalysisPrompt(buildOutput) {
    // 截取构建输出，避免过长
    const maxOutputLength = 8000;
    const truncatedOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    // 生成工具描述
    const toolDescriptions = Object.entries(this.agentTools)
      .map(([name, tool]) => `- ${name}: ${tool.description}`)
      .join('\n');

    return `你是一个专业的 AI Agent 构建错误分析师，具备深度语义理解能力。

**核心任务**：
作为 AI Agent，你需要：
1. 语义化理解构建错误的根本原因
2. 智能识别需要修复的关键文件
3. 提供基于语义的修复策略建议

**错误输出分析**：
\`\`\`
${truncatedOutput}
\`\`\`

**可用 Agent 工具**：
${toolDescriptions}

**AI Agent 分析要求**：
- 使用语义理解而非简单的关键词匹配
- 识别错误的深层原因，而非表面症状
- 考虑 Vue 2 到 Vue 3 迁移的语义变化
- 理解 webpack 配置的语义结构
- 分析依赖关系的语义冲突

**响应格式**：
请使用以下 XML 格式返回 AI Agent 分析结果：

\`\`\`xml
<agent_analysis>
<files_to_fix>
<file>src/components/Example.vue</file>
<file>src/utils/helper.js</file>
</files_to_fix>
<semantic_reasoning>
基于语义理解的修复原因分析：
1. 错误的语义根因
2. 文件选择的语义逻辑
3. 修复策略的语义依据
</semantic_reasoning>
<fix_strategy>
建议的修复策略类型：webpack_config | vue_migration | dependency_conflict | syntax_error
</fix_strategy>
</agent_analysis>
\`\`\`

**语义分析重点**：
- 理解错误的语义含义，而非字面匹配
- 识别 Vue 生态系统的语义变化
- 分析配置文件的语义结构问题
- 理解依赖版本的语义兼容性

请进行深度语义分析，提供智能化的修复建议。`;
  }

  /**
   * 解析 AI Agent 分析响应
   * 支持语义化的响应解析
   */
  parseAnalysisResponse(response) {
    try {
      // 优先解析 AI Agent 格式
      const agentMatch = response.match(/<agent_analysis>[\s\S]*?<files_to_fix>([\s\S]*?)<\/files_to_fix>[\s\S]*?<\/agent_analysis>/);

      if (agentMatch) {
        const filesSection = agentMatch[1];
        const fileMatches = filesSection.match(/<file>(.*?)<\/file>/g);

        if (fileMatches) {
          const files = fileMatches.map(match => {
            let file = match.replace(/<\/?file>/g, '').trim();
            // 如果是绝对路径，转换为相对路径
            if (file.startsWith(this.projectPath)) {
              file = path.relative(this.projectPath, file);
            }
            return file;
          }).filter(file => this.isValidProjectFile(file));

          // 提取语义推理信息
          const reasoningMatch = response.match(/<semantic_reasoning>([\s\S]*?)<\/semantic_reasoning>/);
          if (reasoningMatch && this.options.verbose) {
            console.log(chalk.blue('    🧠 AI Agent 语义推理:'));
            console.log(chalk.gray(`    ${reasoningMatch[1].trim()}`));
          }

          // 提取修复策略
          const strategyMatch = response.match(/<fix_strategy>([\s\S]*?)<\/fix_strategy>/);
          if (strategyMatch && this.options.verbose) {
            console.log(chalk.blue('    🎯 修复策略:'), chalk.cyan(strategyMatch[1].trim()));
          }

          return files;
        }
      }

      // 回退：尝试解析传统格式
      const xmlMatch = response.match(/<analysis>[\s\S]*?<files_to_fix>([\s\S]*?)<\/files_to_fix>[\s\S]*?<\/analysis>/);

      if (xmlMatch) {
        const filesSection = xmlMatch[1];
        const fileMatches = filesSection.match(/<file>(.*?)<\/file>/g);

        if (fileMatches) {
          return fileMatches.map(match => {
            let file = match.replace(/<\/?file>/g, '').trim();
            if (file.startsWith(this.projectPath)) {
              file = path.relative(this.projectPath, file);
            }
            return file;
          }).filter(file => this.isValidProjectFile(file));
        }
      }

      // 最后回退：智能提取文件路径
      return this.extractFilesFromResponse(response);
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI Agent 响应失败，使用智能提取'));
      return this.extractFilesFromResponse(response);
    }
  }

  /**
   * 智能提取文件路径（回退方法）
   */
  extractFilesFromResponse(response) {
    const lines = response.split('\n');
    const files = [];

    for (const line of lines) {
      // 查找看起来像文件路径的行
      const fileMatch = line.match(/(?:src\/|\.\/)?[\w/\-.]+\.(vue|js|ts|jsx|tsx|json)$/);
      if (fileMatch) {
        files.push(fileMatch[0]);
      }
    }

    return [...new Set(files)].filter(file => this.isValidProjectFile(file)); // 去重并过滤
  }

  /**
   * 验证文件是否为有效的项目文件
   */
  isValidProjectFile(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      return false;
    }

    // 过滤掉 node_modules 中的文件
    if (filePath.includes('node_modules')) {
      console.log(chalk.yellow(`    ⚠️  跳过 node_modules 文件: ${filePath}`));
      return false;
    }

    // 过滤掉系统路径
    if (path.isAbsolute(filePath) && !filePath.startsWith(this.projectPath)) {
      console.log(chalk.yellow(`    ⚠️  跳过系统文件: ${filePath}`));
      return false;
    }

    // 只允许特定类型的文件
    const allowedExtensions = ['.vue', '.js', '.ts', '.jsx', '.tsx', '.json'];
    const allowedConfigFiles = ['vue.config.js', 'webpack.config.js', 'vite.config.js', 'vite.config.ts'];

    const ext = path.extname(filePath);
    const fileName = path.basename(filePath);

    if (allowedExtensions.includes(ext) || allowedConfigFiles.includes(fileName)) {
      return true;
    }

    console.log(chalk.yellow(`    ⚠️  跳过不支持的文件类型: ${filePath}`));
    return false;
  }

  /**
   * 修复单个文件
   */
  async fixSingleFileWithAI(filePath, fileContent, buildOutput) {
    try {
      const prompt = this.generateFixPrompt(filePath, fileContent, buildOutput);
      const response = await this.callAI(prompt);

      // 解析修复后的文件内容
      const fixedContent = this.parseFixResponse(response, fileContent);

      if (!fixedContent || fixedContent === fileContent) {
        return {
          success: false,
          error: 'AI 未能生成有效的修复内容'
        };
      }

      return {
        success: true,
        fixedContent
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成文件修复提示词
   */
  generateFixPrompt(filePath, fileContent, buildOutput) {
    const fileExtension = path.extname(filePath);

    // 分析错误类型，生成针对性的提示词
    const errorAnalysis = this.analyzeErrorType(buildOutput);

    if (errorAnalysis.isWebpackConfigError) {
      return this.generateWebpackConfigFixPrompt(filePath, fileContent, buildOutput, errorAnalysis);
    }

    return this.generateGeneralFixPrompt(filePath, fileContent, buildOutput, fileExtension);
  }

  /**
   * 分析错误类型
   */
  analyzeErrorType(buildOutput) {
    const analysis = {
      isWebpackConfigError: false,
      isPluginError: false,
      isLoaderError: false,
      isSyntaxError: false,
      isImportError: false,
      specificErrors: []
    };

    // 检查是否是 webpack 配置错误
    if (buildOutput.includes('ValidationError: Invalid configuration object') ||
        buildOutput.includes('configuration.plugins') ||
        buildOutput.includes('misses the property \'apply\'')) {
      analysis.isWebpackConfigError = true;
      analysis.isPluginError = true;
    }

    // 提取具体的插件错误信息
    const pluginErrorMatch = buildOutput.match(/configuration\.plugins\[(\d+)\] misses the property 'apply'/);
    if (pluginErrorMatch) {
      analysis.specificErrors.push({
        type: 'plugin_missing_apply',
        pluginIndex: parseInt(pluginErrorMatch[1]),
        message: pluginErrorMatch[0]
      });
    }

    // 检查其他错误类型
    if (buildOutput.includes('Module not found') || buildOutput.includes('Cannot resolve module')) {
      analysis.isImportError = true;
    }

    if (buildOutput.includes('SyntaxError') || buildOutput.includes('Unexpected token')) {
      analysis.isSyntaxError = true;
    }

    return analysis;
  }

  /**
   * 生成 webpack 配置修复提示词
   */
  generateWebpackConfigFixPrompt(filePath, fileContent, buildOutput, errorAnalysis) {
    let specificGuidance = '';

    if (errorAnalysis.specificErrors.length > 0) {
      const pluginError = errorAnalysis.specificErrors.find(e => e.type === 'plugin_missing_apply');
      if (pluginError) {
        specificGuidance = `
**特定错误分析**：
- 错误位置：plugins[${pluginError.pluginIndex}] 缺少 'apply' 方法
- 这通常意味着插件配置不正确，可能是：
  1. 传入了空对象 {} 而不是插件实例
  2. 插件构造函数调用错误
  3. 插件版本不兼容

**重点检查**：
- 查找 chainWebpack 中的 config.plugin().use({}) 调用
- 确保所有插件都正确实例化
- 检查是否有空的插件配置对象`;
      }
    }

    return `你是一个专业的 webpack 配置和 Vue 3 迁移专家。请修复以下 webpack 配置文件中的错误。

**任务目标**：修复 webpack 配置错误，确保项目能够成功构建。

**文件信息**：
- 文件路径：${filePath}
- 文件类型：webpack 配置文件

**构建错误输出**：
\`\`\`
${buildOutput}
\`\`\`
${specificGuidance}

**当前文件内容**：
\`\`\`js
${fileContent}
\`\`\`

**webpack 配置修复要求**：
1. 修复插件配置错误，确保所有插件都有有效的 apply 方法
2. 更新 Vue 2 到 Vue 3 的 webpack 配置
3. 修复 chainWebpack 中的插件配置
4. 确保所有插件都正确实例化
5. 保持原有功能不变

**常见 webpack 配置问题修复**：
- config.plugin('name').use({}) → 应该传入有效的插件构造函数
- 删除或修复无效的插件配置
- 更新 Vue 3 兼容的 webpack 插件
- 修复 preload/prefetch 插件配置

**响应格式**：
请使用以下 XML 格式返回修复后的完整文件内容：

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
修复后的完整文件内容
</content>
</file_fix>
\`\`\`

请仔细分析错误信息，特别关注插件配置问题，提供准确的修复方案。`;
  }

  /**
   * 生成通用修复提示词
   */
  generateGeneralFixPrompt(filePath, fileContent, buildOutput, fileExtension) {
    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请修复以下文件中的构建错误。

**任务目标**：修复 Vue 2 到 Vue 3 迁移过程中的构建错误，确保项目能够成功构建。

**文件信息**：
- 文件路径：${filePath}
- 文件类型：${fileExtension}

**构建错误输出**：
\`\`\`
${buildOutput}
\`\`\`

**当前文件内容**：
\`\`\`${fileExtension.slice(1) || 'text'}
${fileContent}
\`\`\`

**修复要求**：
1. 保持原有功能不变
2. 使用 Vue 3 兼容的语法
3. 更新导入语句和组件使用方式
4. 修复 TypeScript 类型错误
5. 确保代码风格一致

**响应格式**：
请使用以下 XML 格式返回修复后的完整文件内容：

\`\`\`xml
<file_fix>
<path>${filePath}</path>
<content>
修复后的完整文件内容
</content>
</file_fix>
\`\`\`

**常见修复模式**：
- Vue 2 → Vue 3: new Vue() → createApp()
- Element UI → Element Plus: 更新导入路径和组件名称
- Vue Router: 更新路由配置语法
- Vuex: 更新状态管理语法
- 组合式 API: 使用 ref, reactive, computed 等

请仔细分析错误信息，提供准确的修复方案。`;
  }

  /**
   * 解析修复响应
   */
  parseFixResponse(response, originalContent) {
    try {
      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<file_fix>[\s\S]*?<content>([\s\S]*?)<\/content>[\s\S]*?<\/file_fix>/);

      if (xmlMatch) {
        const content = xmlMatch[1].trim();
        if (content && content !== originalContent) {
          return content;
        }
      }

      // 回退：尝试解析代码块格式
      const codeBlockMatch = response.match(/```(?:vue|js|ts|javascript|typescript)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        const content = codeBlockMatch[1].trim();
        if (content && content !== originalContent) {
          return content;
        }
      }

      return null;
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 修复响应失败'));
      return null;
    }
  }

  /**
   * 显示成功消息
   */
  displaySuccessMessage() {
    console.log(chalk.green('\n🎉 恭喜！所有构建错误已修复'));
    console.log(chalk.blue('\n💡 后续建议:'));
    console.log(chalk.gray('  1. 运行测试确保功能正常'));
    console.log(chalk.gray('  2. 检查修复后的代码是否符合预期'));
    console.log(chalk.gray('  3. 提交代码变更'));
  }

  /**
   * 创建结果对象
   */
  createResult(success, reason = null, remainingErrors = 0) {
    return {
      success,
      attempts: this.buildStats.buildAttempts,
      errorsFixed: this.buildStats.errorsFixed,
      remainingErrors,
      duration: this.buildStats.duration,
      reason
    };
  }

  /**
   * 执行 Dev 模式检测（30秒运行）
   */
  async performDevCheck() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行 dev 命令并在 30 秒后停止
      return await this.executeDevCommand();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 执行构建过程（包含依赖安装和构建）
   */
  async performBuild() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行构建
      return await this.executeBuild();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 安装项目依赖
   */
  async installDependencies() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过依赖安装'));
      return;
    }

    this.updateSpinner('正在安装依赖...');

    try {
      const installOutput = execSync(this.options.installCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 依赖安装完成'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(installOutput));
        this.startSpinner('继续处理...');
      }
    } catch (error) {
      // 尝试使用 legacy peer deps 重新安装
      if (this.options.useLegacyPeerDeps && this.shouldUseLegacyPeerDeps(error)) {
        await this.installWithLegacyPeerDeps();
      } else {
        this.stopSpinner();
        console.log(chalk.yellow('⚠️ 依赖安装可能不完整，继续尝试构建'));
        this.logInstallError(error);
        this.startSpinner('继续处理...');
      }
    }
  }

  /**
   * 检查是否应该使用 legacy peer deps
   */
  shouldUseLegacyPeerDeps(error) {
    const errorOutput = error.stdout + error.stderr;
    return errorOutput.includes('ERESOLVE unable to resolve dependency tree') ||
           errorOutput.includes('Fix the upstream dependency conflict') ||
           errorOutput.includes('--legacy-peer-deps') ||
           errorOutput.includes('peer dependency') ||
           errorOutput.includes('Could not resolve dependency');
  }

  /**
   * 使用 legacy peer deps 安装依赖
   */
  async installWithLegacyPeerDeps() {
    this.updateSpinner('检测到依赖冲突，尝试使用 --legacy-peer-deps 重新安装...');

    try {
      const legacyCommand = this.options.installCommand + ' --legacy-peer-deps';
      const legacyInstallOutput = execSync(legacyCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        this.stopSpinner();
        console.log(chalk.green('✅ 使用 --legacy-peer-deps 安装依赖成功'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(legacyInstallOutput));
        this.startSpinner('继续处理...');
      }
    } catch (legacyError) {
      this.stopSpinner();
      console.log(chalk.red('❌ 即使使用 --legacy-peer-deps 也无法安装依赖'));
      this.logInstallError(legacyError);
      this.startSpinner('继续处理...');
    }
  }

  /**
   * 执行 Dev 命令（30秒超时）
   */
  async executeDevCommand() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过 dev 命令执行'));
      return { success: true, output: 'DRY RUN - Dev command skipped' };
    }

    const devCommand = this.options.devCommand || 'pnpm dev';
    const timeout = this.options.devTimeout || 30000; // 30秒

    this.updateSpinner(`执行 dev 命令: ${devCommand} (${timeout / 1000}s 超时)`);
    this.buildStats.buildAttempts++;

    return new Promise((resolve) => {
      const { spawn } = require('child_process');
      let output = '';
      let hasErrors = false;

      const child = spawn('sh', ['-c', devCommand], {
        cwd: this.projectPath,
        stdio: 'pipe'
      });

      // 收集输出
      child.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;

        // 检查是否有错误
        if (this.containsErrors(text)) {
          hasErrors = true;
        }

        if (this.options.verbose) {
          process.stdout.write(chalk.gray(text));
        }
      });

      child.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;
        hasErrors = true;

        if (this.options.verbose) {
          process.stderr.write(chalk.red(text));
        }
      });

      // 30秒后停止进程
      const timer = setTimeout(() => {
        child.kill('SIGTERM');

        // 如果进程没有优雅退出，强制杀死
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);

        this.stopSpinner();

        if (hasErrors) {
          console.log(chalk.red('❌ Dev 模式检测到错误'));
          resolve(this.handleBuildError({ stdout: output, stderr: '' }));
        } else {
          console.log(chalk.green('✅ Dev 模式运行正常'));
          resolve({ success: true, output });
        }
      }, timeout);

      child.on('exit', (code) => {
        clearTimeout(timer);
        this.stopSpinner();

        if (code !== 0 || hasErrors) {
          console.log(chalk.red('❌ Dev 命令执行失败'));
          resolve(this.handleBuildError({ stdout: output, stderr: '' }));
        } else {
          console.log(chalk.green('✅ Dev 命令执行成功'));
          resolve({ success: true, output });
        }
      });
    });
  }

  /**
   * 检查文本中是否包含错误
   */
  containsErrors(text) {
    const errorPatterns = [
      /error/i,
      /ERROR/,
      /failed/i,
      /FAILED/,
      /cannot find module/i,
      /module not found/i,
      /syntax error/i,
      /type error/i,
      /compilation error/i,
      /build error/i
    ];

    return errorPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 执行构建命令
   */
  async executeBuild() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过构建执行'));
      return { success: true, output: 'DRY RUN - Build skipped' };
    }

    this.updateSpinner(`执行构建命令: ${this.options.buildCommand}`);
    this.buildStats.buildAttempts++;

    try {
      const output = execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return {
        success: true,
        output
      };
    } catch (error) {
      this.stopSpinner();
      console.log(chalk.red('❌ 构建失败'));
      const result = this.handleBuildError(error);
      this.startSpinner('继续处理...');
      return result;
    }
  }

  /**
   * 处理构建错误
   */
  handleBuildError(error) {
    const errorOutput = (error.stdout || '') + (error.stderr || '');

    // 显示错误摘要
    if (errorOutput.trim()) {
      console.log(chalk.red('\n构建错误信息:'));

      // 提取关键错误行
      const errorLines = errorOutput.split('\n').filter(line => {
        const lowerLine = line.toLowerCase();
        return lowerLine.includes('error') ||
               lowerLine.includes('failed') ||
               lowerLine.includes('cannot find') ||
               lowerLine.includes('module not found') ||
               lowerLine.includes('syntax error') ||
               lowerLine.includes('validationerror');
      });

      // 只显示前3行最关键的错误信息，除非是 verbose 模式
      const displayCount = this.options.verbose ? Math.min(20, errorLines.length) : Math.min(3, errorLines.length);
      errorLines.slice(0, displayCount).forEach(line => {
        console.log(chalk.gray(`  ${line.trim()}`));
      });

      if (errorLines.length > displayCount) {
        const remainingCount = errorLines.length - displayCount;
        if (this.options.verbose) {
          console.log(chalk.gray(`  ... 以及 ${remainingCount} 个其他错误`));
        } else {
          console.log(chalk.gray(`  ... 以及 ${remainingCount} 个其他错误 (使用 --verbose 查看详情)`));
        }
      }

      if (this.options.verbose) {
        console.log(chalk.gray('\n完整错误输出:'));
        console.log(chalk.red(errorOutput));
      }
    } else {
      console.log(chalk.yellow('⚠️ 无法获取具体错误信息'));
    }

    return {
      success: false,
      output: errorOutput,
      error: error.message || '构建失败'
    };
  }

  /**
   * 打印构建统计
   */
  printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.buildStats.buildAttempts} 次`);
    console.log(`发现错误: ${this.buildStats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.buildStats.errorsFixed} 个`);
    console.log(`最终状态: ${this.buildStats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);
  }

}

module.exports = BuildFixAgent;
