const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const os = require('os');
require('dotenv').config();

// 尝试导入 AI 依赖，如果失败则禁用 AI 功能
let generateText, createOpenAI;
let aiAvailable = false;

try {
  const ai = require('ai');
  const aiSdkOpenai = require('@ai-sdk/openai');
  generateText = ai.generateText;
  createOpenAI = aiSdkOpenai.createOpenAI;
  aiAvailable = true;
} catch (error) {
  console.warn(chalk.yellow('⚠️  AI 依赖未安装，AI 修复功能将被禁用'));
  console.warn(chalk.gray('   运行 "npm install ai @ai-sdk/openai" 来启用 AI 功能'));
  aiAvailable = false;
}

// 静态变量，用于避免重复输出警告
let aiWarningShown = false;

/**
 * Configure LLM provider based on available environment variables
 */
function configureLLMProvider() {
  // DeepSeek Provider (Prioritized)
  if (process.env.DEEPSEEK_TOKEN) {
    const openai = createOpenAI({
      compatibility: "compatible",
      baseURL: process.env.DEEPSEEK_BASE_URL || "https://api.deepseek.com/v1",
      apiKey: process.env.DEEPSEEK_TOKEN,
    });

    return {
      fullModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
      quickModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
      openai,
      providerName: "DeepSeek"
    };
  }

  // GLM Provider (智谱AI)
  if (process.env.GLM_API_KEY || process.env.GLM_TOKEN) {
    const apiKey = process.env.GLM_API_KEY || process.env.GLM_TOKEN;
    const openai = createOpenAI({
      compatibility: "compatible",
      baseURL: process.env.LLM_BASE_URL || "https://open.bigmodel.cn/api/paas/v4",
      apiKey: apiKey,
    });

    return {
      fullModel: process.env.LLM_MODEL || "glm-4-air",
      quickModel: process.env.LLM_MODEL || "glm-4-air",
      openai,
      providerName: "GLM"
    };
  }

  // OpenAI Provider
  if (process.env.OPENAI_API_KEY) {
    const openai = createOpenAI({
      compatibility: "strict",
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL,
    });

    return {
      fullModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
      quickModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
      openai,
      providerName: "OpenAI"
    };
  }

  return null;
}

/**
 * Check if any LLM provider is available
 */
function hasLLMProvider() {
  return configureLLMProvider() !== null;
}

/**
 * Get provider status for debugging
 */
function getLLMProviderStatus() {
  return {
    GLM: !!(process.env.GLM_API_KEY || process.env.GLM_TOKEN),
    DeepSeek: !!process.env.DEEPSEEK_TOKEN,
    OpenAI: !!process.env.OPENAI_API_KEY,
    Anthropic: !!process.env.ANTHROPIC_API_KEY
  };
}

/**
 * AI 服务基类
 * 提供通用的 AI 调用功能，可被其他模块继承使用
 */
class AiService {
  constructor(options = {}) {
    console.log(chalk.gray(`🔍 AIService 接收到的 options.logDir: ${options.logDir}`));

    this.options = {
      maxTokens: options.maxTokens || 4000, // 减少 token 数量以提高响应速度
      temperature: options.temperature || 0.1, // 稍微增加温度以提高生成速度
      maxRetries: options.maxRetries || 3,
      logDir: options.logDir || path.join(process.cwd(), 'ai-logs'),
      // 只添加其他选项，不覆盖已设置的默认值
      ...Object.fromEntries(
        Object.entries(options).filter(([key]) =>
          !['maxTokens', 'temperature', 'maxRetries', 'logDir'].includes(key)
        )
      )
    };

    console.log(chalk.gray(`🔍 AIService 设置的 this.options.logDir: ${this.options.logDir}`));

    // 配置 LLM 提供商
    this.llmConfig = null;
    if (aiAvailable) {
      this.llmConfig = configureLLMProvider();
      if (this.llmConfig) {
        this.enabled = true;
        console.log(chalk.green(`✅ AI 服务已启用 (${this.llmConfig.providerName})`));
      } else {
        this.enabled = false;
        // 只在第一次显示警告
        if (!aiWarningShown) {
          console.warn(chalk.yellow('⚠️  未找到可用的 LLM 提供商，AI 服务将被禁用'));
          console.warn(chalk.gray('   请设置以下环境变量之一: DEEPSEEK_TOKEN, GLM_TOKEN, OPENAI_API_KEY'));
          aiWarningShown = true;
        }
      }
    } else {
      this.enabled = false;
    }

    this.stats = {
      attempted: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 检查 AI 服务是否可用
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 调用 AI API
   */
  async callAI(prompt, options = {}) {
    if (!aiAvailable || !this.llmConfig) {
      throw new Error('AI 功能不可用，请安装相关依赖或配置 LLM 提供商');
    }

    const callOptions = {
      maxTokens: options.maxTokens || this.options.maxTokens,
      temperature: options.temperature || this.options.temperature,
      maxRetries: options.maxRetries || this.options.maxRetries,
      context: options.context || {}
    };

    // 创建日志目录（如果不存在）
    await fs.ensureDir(this.options.logDir);

    // 生成唯一的日志文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const randomId = Math.random().toString(36).substring(2, 8);
    const logFileName = `ai-call-${timestamp}-${randomId}.json`;
    const logFilePath = path.join(this.options.logDir, logFileName);

    // 准备日志内容
    const logData = {
      timestamp: new Date().toISOString(),
      provider: this.llmConfig ? this.llmConfig.providerName : 'unknown',
      model: this.llmConfig ? this.llmConfig.fullModel : 'unknown',
      options: {
        maxTokens: callOptions.maxTokens,
        temperature: callOptions.temperature
      },
      context: {
        ...callOptions.context,
        fileName: options.fileName || 'unknown',
        fileType: options.fileType || 'unknown',
        taskType: options.taskType || 'general'
      },
      request: prompt,
      response: null,
      success: false,
      attempts: 0,
      error: null,
      duration_ms: 0
    };

    const startTime = Date.now();
    let lastError;

    for (let attempt = 1; attempt <= callOptions.maxRetries; attempt++) {
      logData.attempts = attempt;
      try {
        console.log(chalk.gray(`🤖 调用 ${this.llmConfig.providerName} API (尝试 ${attempt}/${callOptions.maxRetries})...`));

        const requestStartTime = Date.now();
        const { text } = await generateText({
          model: this.llmConfig.openai(this.llmConfig.fullModel),
          prompt: prompt,
          maxTokens: callOptions.maxTokens,
          temperature: callOptions.temperature,
        });
        const requestDuration = Date.now() - requestStartTime;

        // 记录原始响应
        logData.response = text;
        logData.success = true;
        logData.duration_ms = requestDuration;

        // 提取代码块
        const codeMatch = text.match(/```[\w]*\n([\s\S]*?)\n```/);
        let result;

        if (codeMatch) {
          result = codeMatch[1];
          logData.extractedCode = true;
        } else {
          // 如果没有代码块，返回整个响应
          result = text.trim();
          logData.extractedCode = false;
        }

        // 异步写入日志文件，不等待完成
        this.writeLogFile(logFilePath, logData).catch(err => {
          console.warn(chalk.yellow(`⚠️  写入 AI 调用日志失败: ${err.message}`));
        });

        console.log(chalk.green(`✅ AI 响应成功 (${requestDuration}ms)`));
        return result;
      } catch (error) {
        lastError = error;
        console.log(chalk.yellow(`⚠️  AI 调用失败 (尝试 ${attempt}/${callOptions.maxRetries}): ${error.message}`));

        // 记录错误信息
        logData.error = {
          message: error.message,
          stack: error.stack
        };

        if (attempt < callOptions.maxRetries) {
          // 等待后重试
          const waitTime = 1000 * attempt;
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // 记录最终失败
    logData.duration_ms = Date.now() - startTime;
    await this.writeLogFile(logFilePath, logData).catch(() => {});

    throw new Error(`AI 调用失败，已重试 ${callOptions.maxRetries} 次: ${lastError.message}`);
  }

  /**
   * 写入日志文件
   * @private
   */
  async writeLogFile(logFilePath, logData) {
    try {
      await fs.writeJson(logFilePath, logData, { spaces: 2 });
      console.log(chalk.gray(`📝 AI 调用日志已保存: ${path.basename(logFilePath)}`));
      return true;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法写入日志文件: ${error.message}`));
      return false;
    }
  }

  /**
   * 验证修复后的内容（基础验证）
   */
  validateRepairedContent(repairedContent, originalContent) {
    // 基本验证
    if (!repairedContent || repairedContent.trim().length === 0) {
      return false;
    }

    // 检查是否有明显的错误标记
    const errorMarkers = ['ERROR', 'FIXME', 'TODO: Fix', '// BROKEN'];
    if (errorMarkers.some(marker => repairedContent.includes(marker))) {
      return false;
    }

    // 检查代码长度是否合理（不应该太短或太长）
    const lengthRatio = repairedContent.length / originalContent.length;
    if (lengthRatio < 0.3 || lengthRatio > 3) {
      return false;
    }

    // 检查是否包含基本的代码结构
    // if (originalContent.includes('export default') && !repairedContent.includes('export')) {
    //   return false;
    // }

    return true;
  }

  /**
   * 备份文件
   */
  async backupFile(filePath, suffix = 'ai-backup') {
    const backupPath = `${filePath}.${suffix}`;
    await fs.copy(filePath, backupPath);
    console.log(chalk.gray(`📁 已备份文件: ${backupPath}`));
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      attempted: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 获取 LLM 提供商状态
   */
  getProviderStatus() {
    return {
      enabled: this.enabled,
      provider: this.llmConfig ? this.llmConfig.providerName : null,
      availableProviders: getLLMProviderStatus()
    };
  }
}

module.exports = {
  AIService: AiService,
  configureLLMProvider,
  hasLLMProvider,
  getLLMProviderStatus,
  aiAvailable
};
